# LMS System Change Guide - Go-To Report

## 🎯 Quick Navigation for System Changes

This document provides a quick reference for locating and modifying different aspects of the Academy LMS system.

---

## 📁 File Structure Overview

```
Academy-LMS/
├── project/                    # Main Django project
│   ├── settings.py            # Core configuration
│   ├── urls.py               # Main URL routing
│   └── wsgi.py               # WSGI configuration
├── website/                   # Main application
│   ├── models.py             # Database models
│   ├── views.py              # Main view functions
│   ├── urls.py               # Website URL patterns
│   ├── admin.py              # Admin interface
│   ├── forms.py              # Form definitions
│   ├── middleware.py         # Custom middleware
│   ├── backends.py           # Authentication backends
│   ├── decorators.py         # Access control decorators
│   ├── templates/            # HTML templates
│   └── migrations/           # Database migrations
├── api/                      # REST API application
├── core/                     # Core utilities
├── admins/                   # Admin-specific functionality
└── static/                   # Static files (CSS, JS, images)
```

---

## 🔧 Common Change Scenarios

### 1. Adding New User Types

**Files to Modify:**
- `website/models.py` - Add new UserType entry
- `website/admin.py` - Register admin interface
- `website/views.py` - Add user type-specific views
- `website/urls.py` - Add new URL patterns
- `website/templates/` - Create user-specific templates

**Steps:**
1. Create UserType in database via admin or migration
2. Update authentication logic in `backends.py`
3. Add role-based access in `decorators.py`
4. Create dashboard view in `dashboard_views.py`
5. Add URL routing in `urls.py`

### 2. Adding New Course Features

**Files to Modify:**
- `website/models.py` - Course model modifications
- `website/forms.py` - Course creation/edit forms
- `website/views.py` - Course management views
- `website/templates/website/courses/` - Course templates
- `website/admin.py` - Admin interface updates

**Key Models:**
- `Course` - Main course definition
- `CourseInstance` - Scheduled course instances
- `Session` - Individual course sessions
- `CourseContent` - Course materials

### 3. Modifying Email Notifications

**Files to Modify:**
- `corporate_email_utils.py` - Corporate notifications
- `individual_email_utils.py` - Individual user notifications
- `trainer_email_utils.py` - Trainer notifications
- `GB_email_utils.py` - GB employee notifications
- `otp_email_utils.py` - OTP-related emails

**Email Trigger Locations:**
- Corporate: `website/corporate_views.py`
- Individual: `website/individual_views.py`
- Trainer: `website/views_trainer.py`
- GB Employee: `website/GB_views.py`

### 4. Adding New API Endpoints

**Files to Modify:**
- `api/views.py` - API view functions
- `api/serializers.py` - Data serialization
- `api/urls.py` - API URL patterns
- `website/urls.py` - Include API URLs

**REST Framework Configuration:**
- `project/settings.py` - REST_FRAMEWORK settings
- Authentication classes and permissions

### 5. Database Schema Changes

**Process:**
1. Modify models in `website/models.py`
2. Create migration: `python manage.py makemigrations`
3. Apply migration: `python manage.py migrate`
4. Update admin interface in `website/admin.py`
5. Update forms in `website/forms.py`

### 6. Authentication & Access Control

**Files to Modify:**
- `website/backends.py` - Authentication backends
- `website/middleware.py` - Access control middleware
- `website/decorators.py` - Permission decorators
- `project/settings.py` - Authentication settings

**Key Components:**
- `EmailOrUsernameModelBackend` - External user auth
- `OracleEmployeeBackend` - GB employee auth
- `ForcePasswordResetMiddleware` - Password enforcement
- `AdminITGroupMiddleware` - Admin access control

### 7. Frontend/Template Changes

**Template Structure:**
- `website/templates/website/base.html` - Main layout
- `website/templates/website/dashboard/` - Dashboard templates
- `website/templates/website/courses/` - Course templates
- `website/templates/website/calendar/` - Calendar views

**Static Files:**
- `static/css/` - Stylesheets
- `static/js/` - JavaScript files
- `static/images/` - Image assets

### 8. Integration Updates

**Oracle EBS Integration:**
- `website/soap_client.py` - SOAP API client
- `website/models.py` - GB_Employee model
- `website/otp_views.py` - OTP authentication
- `core/constants.py` - API endpoints

**Payment Integration:**
- `core/paymob.py` - Payment gateway
- `website/views.py` - Payment processing
- `core/constants.py` - Payment configuration

**SMS Integration:**
- `core/vodafone.py` - SMS service
- `website/otp_views.py` - OTP delivery
- `core/constants.py` - SMS configuration

---

## 🗄️ Database Model Quick Reference

### Core Models Location: `website/models.py`

**User Management:**
- `CustomUser` - Main user model
- `UserType` - User role definitions
- `GB_Employee` - Oracle employee sync

**Learning Management:**
- `Course` - Course definitions
- `CourseInstance` - Scheduled courses
- `Session` - Individual sessions
- `Reservation` - User enrollments
- `Attendance` - Session attendance

**Resource Management:**
- `Room` - Training rooms
- `Equipment` - Training equipment
- `Trainer` - Instructor profiles
- `RoomAvailability` - Room scheduling
- `TrainerAvailability` - Trainer scheduling

**Assessment:**
- `Questionnaire` - Course assessments
- `QuestionnaireResponse` - User responses
- `Survey` - Feedback surveys
- `SurveyResponse` - Survey responses

**Corporate Management:**
- `Corporate` - Company profiles
- `CorporateAdmin` - Corporate administrators
- `CorporateAdminRequest` - Course requests

---

## 🔗 URL Pattern Quick Reference

### Main URLs: `project/urls.py`
- `/dzJAMvwB/` - Admin panel
- `/api/v1/` - REST API
- `/api/otp/` - OTP authentication
- `/` - Main website (i18n)

### Website URLs: `website/urls.py`
- `/dashboard-redirect/` - Smart dashboard routing
- `/courses/` - Course management
- `/calendar/` - Calendar views
- `/my-reservations/` - User reservations
- `/trainer/` - Trainer-specific views
- `/corporate/` - Corporate admin views
- `/GB_supervisor/` - GB supervisor views

---

## ⚙️ Configuration Files

### Main Settings: `project/settings.py`
- Database configuration
- Email settings
- Authentication backends
- Middleware configuration
- Static/media file settings
- Internationalization settings

### Environment Variables (.env)
- `SECRET_KEY` - Django secret key
- `DEBUG` - Debug mode flag
- `SQL_*` - Database credentials
- `EMAIL_*` - Email server settings
- `ORACLE_*` - Oracle API settings
- `PAYMOB_*` - Payment gateway settings
- `VODAFONE_*` - SMS service settings

---

## 🚀 Deployment & Maintenance

### Docker Configuration
- `Dockerfile` - Application container
- `docker-compose.yaml` - Development setup
- `docker-compose-prod.yaml` - Production setup
- `nginx/` - Nginx configuration

### Database Management
- `manage.py migrate` - Apply migrations
- `manage.py makemigrations` - Create migrations
- `manage.py collectstatic` - Collect static files
- `manage.py createsuperuser` - Create admin user

### Cron Jobs: `core/crons.py`
- Log purging (daily at midnight)
- Configured in `project/settings.py`

---

## 📝 Change Log Template

When making changes, document them using this template:

```markdown
## Change Log Entry - [Date]

### Change Type: [Feature/Bug Fix/Enhancement/Configuration]

### Files Modified:
- `path/to/file1.py` - Description of changes
- `path/to/file2.html` - Description of changes

### Database Changes:
- [ ] New migrations created
- [ ] Migration applied
- [ ] Admin interface updated

### Testing:
- [ ] Unit tests updated
- [ ] Manual testing completed
- [ ] User acceptance testing

### Deployment Notes:
- [ ] Environment variables updated
- [ ] Static files collected
- [ ] Cache cleared
- [ ] Services restarted

### Rollback Plan:
- Steps to revert changes if needed
```

---

## 🔍 Debugging & Troubleshooting

### Log Files
- Django logs: Check `DEBUG` setting and console output
- API logs: `drf_api_logger` database table
- Email logs: `EmailLog` model in database
- External service logs: `WebServiceLog` model

### Common Issues
- **Authentication problems**: Check `website/backends.py`
- **Permission errors**: Check `website/decorators.py` and middleware
- **Email not sending**: Check email backend configuration
- **Oracle integration**: Check SOAP client and credentials
- **Payment issues**: Check Paymob configuration and logs

---

## 📋 Quick Reference Checklists

### Pre-Change Checklist
- [ ] Backup current database
- [ ] Document current system state
- [ ] Identify all affected components
- [ ] Plan rollback strategy
- [ ] Test in development environment

### Post-Change Checklist
- [ ] Run all migrations
- [ ] Update admin interface if needed
- [ ] Test all affected user flows
- [ ] Update documentation
- [ ] Monitor system logs for errors
- [ ] Verify email notifications work
- [ ] Test API endpoints if modified
- [ ] Check integration points (Oracle, Paymob, SMS)

### Emergency Rollback Checklist
- [ ] Stop application services
- [ ] Restore database backup
- [ ] Revert code changes
- [ ] Restart services
- [ ] Verify system functionality
- [ ] Notify stakeholders

---

## � Frontend User Flows by User Type

### 1. System Admin Flow

**Landing Page:** `/admin/dashboard/`
**Template:** `website/templates/website/dashboard/admin_dashboard.html`

**Main Navigation:**
- **Course Management**
  - `/courses/` - View all courses
  - `/courses/create/` - Create new course
  - `/courses/<course_id>/edit/` - Edit course details
  - `/courses/<course_id>/instances/` - Manage course instances
  - `/courses/<course_id>/contents/` - Upload course materials

- **User Management**
  - `/users/` - View all users
  - `/users/pending/` - Approve pending registrations
  - `/users/<user_id>/profile/` - View/edit user profiles
  - `/users/corporate-admins/` - Manage corporate admins

- **Resource Management**
  - `/rooms/` - Manage training rooms
  - `/rooms/create/` - Add new room
  - `/equipment/` - Manage equipment
  - `/trainers/` - Manage trainer profiles

- **Calendar & Scheduling**
  - `/calendar/` - Master calendar view
  - `/calendar/room-schedule/` - Room availability
  - `/calendar/trainer-schedule/` - Trainer availability

- **Reports & Analytics**
  - `/reports/attendance/` - Attendance reports
  - `/reports/revenue/` - Revenue analytics
  - `/reports/utilization/` - Resource utilization

**Key Actions:**
- Approve/reject user registrations
- Create and schedule course instances
- Assign trainers to courses
- Manage room and equipment bookings
- Generate system reports
- Configure system settings

### 2. Super Admin Flow

**Landing Page:** `/super-admin/dashboard/`
**Template:** `website/templates/website/dashboard/super_admin_dashboard.html`

**Extended Permissions:**
- All System Admin capabilities PLUS:
- **System Configuration**
  - `/admin/` - Django admin panel access
  - `/system/settings/` - System-wide settings
  - `/system/integrations/` - External system configs

- **Advanced User Management**
  - Create/modify user types
  - Bulk user operations
  - System-wide user analytics

**Key Actions:**
- Full system administration
- Database management
- Integration configuration
- Advanced reporting
- System maintenance

### 3. Corporate Admin Flow

**Landing Page:** `/corporate/dashboard/`
**Template:** `website/templates/website/dashboard/corporate_dashboard.html`

**Main Navigation:**
- **Employee Management**
  - `/corporate/employees/` - Manage company employees
  - `/corporate/employees/add/` - Add new employee
  - `/corporate/employees/<user_id>/` - Employee profile

- **Course Requests**
  - `/corporate/course-requests/` - View all requests
  - `/corporate/course-requests/create/` - Request new course
  - `/corporate/course-requests/<request_id>/` - Request details

- **Reservations Management**
  - `/corporate/reservations/` - Company reservations
  - `/corporate/reservations/bulk-enroll/` - Bulk enrollment
  - `/corporate/reservations/<reservation_id>/cancel/` - Cancel reservations

- **Calendar & Scheduling**
  - `/corporate-calendar/` - Company training calendar
  - `/corporate/availability/` - Check course availability

- **Reports**
  - `/corporate/reports/attendance/` - Employee attendance
  - `/corporate/reports/progress/` - Training progress
  - `/corporate/reports/certificates/` - Completion certificates

**Key Actions:**
- Request courses for employees
- Enroll/cancel employee reservations
- Track employee training progress
- Generate company training reports
- Manage employee profiles

### 4. Individual User Flow

**Landing Page:** `/individual/dashboard/`
**Template:** `website/templates/website/dashboard/individual_dashboard.html`

**Main Navigation:**
- **Course Catalog**
  - `/courses/catalog/` - Browse available courses
  - `/courses/<course_id>/details/` - Course details
  - `/courses/search/` - Search courses by category

- **My Learning**
  - `/my-reservations/` - My course enrollments
  - `/my-calendar/` - Personal training calendar
  - `/my-certificates/` - Earned certificates
  - `/my-progress/` - Learning progress

- **Enrollment Process**
  - `/courses/<instance_id>/enroll/` - Course enrollment
  - `/payment/` - Payment processing
  - `/enrollment/confirmation/` - Enrollment confirmation

- **Assessments**
  - `/questionnaires/` - Pending questionnaires
  - `/questionnaires/<questionnaire_id>/` - Take assessment
  - `/surveys/` - Course feedback surveys

- **Profile Management**
  - `/profile/` - Personal profile
  - `/profile/edit/` - Edit profile information
  - `/profile/password/` - Change password

**Key Actions:**
- Browse and search courses
- Enroll in courses and make payments
- Complete assessments and surveys
- Track learning progress
- Download certificates
- Manage personal profile

### 5. Trainer Flow

**Landing Page:** `/trainer/dashboard/`
**Template:** `website/templates/website/dashboard/trainer_dashboard.html`

**Main Navigation:**
- **My Sessions**
  - `/trainer/sessions/` - Assigned sessions
  - `/trainer/sessions/upcoming/` - Upcoming sessions
  - `/trainer/sessions/<session_id>/` - Session details

- **Attendance Management**
  - `/trainer/attendance/` - Record attendance
  - `/trainer/attendance/<session_id>/` - Session attendance
  - `/trainer/attendance/reports/` - Attendance reports

- **Assessment Tools**
  - `/trainer/questionnaires/` - Create questionnaires
  - `/trainer/questionnaires/<questionnaire_id>/` - Review responses
  - `/trainer/questionnaires/results/` - Assessment results

- **Course Materials**
  - `/trainer/materials/` - Upload course materials
  - `/trainer/materials/<course_id>/` - Course-specific materials

- **Calendar**
  - `/trainer-calendar/` - Personal training calendar
  - `/trainer/availability/` - Set availability
  - `/trainer/schedule/` - View schedule

**Key Actions:**
- View assigned sessions and courses
- Record student attendance
- Create and grade assessments
- Upload course materials
- Manage personal schedule
- Generate session reports

### 6. GB Employee Flow

**Landing Page:** `/internal-gb/dashboard/`
**Template:** `website/templates/website/dashboard/gb_dashboard.html`

**Authentication Flow:**
1. `/otp/request/` - Enter Employee ID
2. `/otp/verify/` - Enter SMS OTP
3. Auto-redirect to dashboard

**Main Navigation:**
- **Course Enrollment**
  - `/gb/courses/` - Available internal courses
  - `/gb/courses/<course_id>/request/` - Request enrollment
  - `/gb/requests/` - My enrollment requests
  - `/gb/requests/<request_id>/status/` - Request status

- **My Learning**
  - `/my-gb-reservations/` - My enrollments
  - `/gb-calendar/` - Personal calendar
  - `/gb/certificates/` - Earned certificates

- **Team Management** (for Supervisors)
  - `/GB_supervisor/dashboard/` - Supervisor dashboard
  - `/GB_supervisor/requests/` - Team requests
  - `/GB_supervisor/requests/<request_id>/approve/` - Approve/reject
  - `/GB_supervisor/team/` - Team members
  - `/GB_supervisor/reports/` - Team reports

**Key Actions:**
- Request course enrollment (requires supervisor approval)
- View enrollment status
- Access personal training calendar
- Complete assessments
- Supervisors: Approve/reject team requests
- Supervisors: Monitor team training progress

---

## 🔄 Complete System Flows & Use Cases

### Registration Flows

**External Individual Registration:**
1. `/` → Select "Individual Registration"
2. `/registration/individual/step1/` → Basic info
3. `/registration/individual/step2/` → Additional details
4. `/registration/individual/step3/` → Account verification
5. `/registration/complete/` → Registration complete
6. Email verification → Account activation

**Corporate Admin Registration:**
1. `/` → Select "Corporate Admin"
2. `/registration/corporate/step1/` → Personal info
3. `/registration/corporate/step2/` → Company details
4. `/registration/corporate/step3/` → Document upload
5. `/registration/pending/` → Awaiting approval
6. Admin approval → Account activation

**GB Employee Authentication:**
1. `/otp/request/` → Enter Employee ID
2. Oracle EBS validation → SMS OTP sent
3. `/otp/verify/` → Enter OTP code
4. Auto-registration if new user
5. `/internal-gb/dashboard/` → Dashboard access

### Course Enrollment Flows

**Individual User Enrollment:**
1. `/courses/catalog/` → Browse courses
2. `/courses/<course_id>/details/` → View details
3. `/courses/<instance_id>/enroll/` → Enrollment form
4. `/payment/` → Payment processing
5. `/enrollment/confirmation/` → Confirmation
6. Email confirmation → Calendar integration

**Corporate Enrollment:**
1. `/corporate/course-requests/create/` → Request course
2. Admin review → Course approval/rejection
3. `/corporate/reservations/bulk-enroll/` → Enroll employees
4. Employee notifications → Calendar updates
5. `/corporate/reports/` → Track progress

**GB Employee Enrollment:**
1. `/gb/courses/<course_id>/request/` → Submit request
2. Oracle EBS → Identify supervisor
3. Supervisor email → Approval request
4. `/GB_supervisor/requests/<request_id>/approve/` → Decision
5. Employee notification → Enrollment confirmation

### Assessment Flows

**Questionnaire Flow:**
1. Trainer creates: `/trainer/questionnaires/create/`
2. System assigns to enrolled users
3. User notification → Email alert
4. `/questionnaires/<questionnaire_id>/` → Complete assessment
5. `/trainer/questionnaires/<questionnaire_id>/review/` → Trainer review
6. Score/feedback → User notification

**Survey Flow:**
1. Admin creates: `/admin/surveys/create/`
2. Auto-assignment to course completers
3. `/surveys/<survey_id>/` → User completes
4. `/admin/surveys/<survey_id>/results/` → View results
5. Analytics dashboard → System improvements

### Cancellation Flows

**Standard Cancellation:**
1. User: `/my-reservations/<reservation_id>/cancel/`
2. System check → Cancellation policy
3. Auto-approval if within deadline
4. Refund processing → Payment gateway
5. Email confirmation → Calendar update

**Emergency Cancellation:**
1. User: `/emergency-cancel/<reservation_id>/`
2. Admin notification → Review request
3. `/admin/cancellations/<request_id>/review/` → Decision
4. User notification → Refund processing
5. System updates → Waitlist management

**Corporate Cancellation:**
1. Corporate admin: `/corporate/reservations/<reservation_id>/cancel/`
2. Employee notification → Cancellation alert
3. System updates → Resource reallocation
4. Reporting updates → Analytics refresh

### Payment & Booking Flows

**Payment Processing Flow:**
1. Course enrollment → Payment required
2. `/payment/checkout/` → Payment gateway redirect
3. Paymob processing → Payment confirmation
4. `/payment/success/` → Success page
5. Email receipt → Enrollment confirmation
6. Calendar integration → Course access

**Waitlist Management:**
1. Course full → User joins waitlist
2. Cancellation occurs → Waitlist notification
3. `/waitlist/confirm/<reservation_id>/` → User confirms
4. Auto-enrollment → Payment processing
5. Confirmation flow → Course access

### Resource Management Flows

**Room Booking Flow:**
1. Admin: `/rooms/calendar/` → View availability
2. `/courses/<instance_id>/assign-room/` → Room assignment
3. Conflict check → Availability validation
4. Booking confirmation → Calendar update
5. Equipment assignment → Resource allocation

**Trainer Assignment Flow:**
1. Admin: `/courses/<instance_id>/assign-trainer/`
2. Trainer availability check → Conflict detection
3. Assignment confirmation → Trainer notification
4. Calendar integration → Session preparation
5. Material access → Course delivery

### Notification & Communication Flows

**Email Notification Triggers:**
- User registration → Welcome email
- Course enrollment → Confirmation email
- Payment success → Receipt email
- Session reminder → 24h before session
- Assessment available → Questionnaire alert
- Cancellation → Refund notification
- Supervisor approval → Decision email

**SMS Notification Triggers:**
- GB Employee OTP → Authentication code
- Emergency alerts → System notifications
- Last-minute changes → Schedule updates

### Reporting & Analytics Flows

**Attendance Reporting:**
1. Trainer: `/trainer/attendance/<session_id>/` → Record attendance
2. System processing → Attendance calculation
3. `/reports/attendance/` → Generate reports
4. Export functionality → PDF/Excel download
5. Email distribution → Stakeholder notifications

**Performance Analytics:**
1. Data collection → User interactions
2. System processing → Analytics calculation
3. Dashboard updates → Real-time metrics
4. Report generation → Scheduled reports
5. Stakeholder distribution → Email delivery

---

## 🎯 All Available Actions by User Type

### System Admin Actions
**Course Management:**
- ✅ Create new courses
- ✅ Edit course details
- ✅ Create course instances
- ✅ Assign trainers to courses
- ✅ Upload course materials
- ✅ Set course prerequisites
- ✅ Manage course categories
- ✅ Archive/deactivate courses

**User Management:**
- ✅ Approve/reject registrations
- ✅ View all user profiles
- ✅ Edit user information
- ✅ Reset user passwords
- ✅ Manage user types
- ✅ Bulk user operations
- ✅ Export user data

**Resource Management:**
- ✅ Create/edit rooms
- ✅ Manage equipment inventory
- ✅ Set room/equipment availability
- ✅ Assign resources to sessions
- ✅ Track resource utilization
- ✅ Generate resource reports

**System Operations:**
- ✅ Generate all reports
- ✅ Configure system settings
- ✅ Manage holidays/blackout dates
- ✅ Monitor system performance
- ✅ Backup/restore data
- ✅ Manage integrations

### Corporate Admin Actions
**Employee Management:**
- ✅ Add company employees
- ✅ Edit employee profiles
- ✅ Bulk employee import
- ✅ Manage employee access
- ✅ Track employee progress
- ✅ Generate employee reports

**Course Operations:**
- ✅ Request courses for company
- ✅ Bulk enroll employees
- ✅ Cancel employee enrollments
- ✅ Track course completion
- ✅ Download certificates
- ✅ Manage training calendar

**Reporting:**
- ✅ Attendance reports
- ✅ Progress tracking
- ✅ Completion certificates
- ✅ Training ROI analysis
- ✅ Export training data

---

## �🎯 Specific Change Scenarios

### Individual User Actions
**Learning Activities:**
- ✅ Browse course catalog
- ✅ Search courses by category
- ✅ View course details
- ✅ Enroll in courses
- ✅ Make payments
- ✅ Access course materials
- ✅ Complete assessments
- ✅ Submit surveys

**Personal Management:**
- ✅ View personal calendar
- ✅ Track learning progress
- ✅ Download certificates
- ✅ Manage profile
- ✅ Change password
- ✅ Cancel enrollments
- ✅ Request refunds

### Trainer Actions
**Session Management:**
- ✅ View assigned sessions
- ✅ Access session details
- ✅ Record attendance
- ✅ Upload session materials
- ✅ Manage session schedule
- ✅ Generate session reports

**Assessment Tools:**
- ✅ Create questionnaires
- ✅ Review student responses
- ✅ Provide feedback/scores
- ✅ Track student progress
- ✅ Generate assessment reports
- ✅ Export results

**Calendar & Availability:**
- ✅ Set availability
- ✅ View training calendar
- ✅ Manage conflicts
- ✅ Request schedule changes

### GB Employee Actions
**Course Enrollment:**
- ✅ Browse internal courses
- ✅ Request course enrollment
- ✅ Track request status
- ✅ View enrollment history
- ✅ Access course materials
- ✅ Complete assessments

**Personal Learning:**
- ✅ View personal calendar
- ✅ Track progress
- ✅ Download certificates
- ✅ Complete surveys
- ✅ Update profile

### GB Supervisor Actions
**Team Management:**
- ✅ View team requests
- ✅ Approve/reject enrollments
- ✅ Add approval comments
- ✅ Track team progress
- ✅ Generate team reports
- ✅ Manage team calendar

**Reporting:**
- ✅ Team attendance reports
- ✅ Progress tracking
- ✅ Completion analysis
- ✅ Export team data

---

## 🔄 Complete System Action Matrix

### Authentication & Access Actions
| Action | System Admin | Super Admin | Corporate Admin | Individual | Trainer | GB Employee | GB Supervisor |
|--------|:------------:|:-----------:|:---------------:|:----------:|:-------:|:-----------:|:-------------:|
| Login with password | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| Login with OTP | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| Reset password | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| Force password reset | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Manage user accounts | ✅ | ✅ | 🔸 | ❌ | ❌ | ❌ | 🔸 |

### Course Management Actions
| Action | System Admin | Super Admin | Corporate Admin | Individual | Trainer | GB Employee | GB Supervisor |
|--------|:------------:|:-----------:|:---------------:|:----------:|:-------:|:-----------:|:-------------:|
| Create courses | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Edit course details | ✅ | ✅ | ❌ | ❌ | 🔸 | ❌ | ❌ |
| Create course instances | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Browse courses | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Enroll in courses | ❌ | ❌ | 🔸 | ✅ | ❌ | 🔸 | ❌ |
| Request courses | ❌ | ❌ | ✅ | ❌ | ❌ | ✅ | ❌ |

### Assessment & Feedback Actions
| Action | System Admin | Super Admin | Corporate Admin | Individual | Trainer | GB Employee | GB Supervisor |
|--------|:------------:|:-----------:|:---------------:|:----------:|:-------:|:-----------:|:-------------:|
| Create questionnaires | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ |
| Complete assessments | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ |
| Grade assessments | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ |
| Create surveys | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Complete surveys | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ |
| View survey results | ✅ | ✅ | 🔸 | ❌ | 🔸 | ❌ | 🔸 |

### Reporting & Analytics Actions
| Action | System Admin | Super Admin | Corporate Admin | Individual | Trainer | GB Employee | GB Supervisor |
|--------|:------------:|:-----------:|:---------------:|:----------:|:-------:|:-----------:|:-------------:|
| Generate all reports | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| View company reports | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| View personal reports | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ❌ |
| View team reports | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ |
| Export data | ✅ | ✅ | ✅ | 🔸 | 🔸 | 🔸 | ✅ |

**Legend:**
- ✅ Full Access
- 🔸 Limited Access (specific conditions apply)
- ❌ No Access

---

## 🎯 Specific Change Scenarios

### Adding New Dashboard Widget
**Files:** `website/templates/website/dashboard/`, `website/views.py`
**Steps:** Create widget template → Add view logic → Update dashboard template → Test

### Modifying Course Enrollment Logic
**Files:** `website/views.py`, `website/models.py`, `website/forms.py`
**Steps:** Update model → Create migration → Modify view → Update form → Test workflow

### Adding New Email Template
**Files:** `*_email_utils.py`, `website/templates/emails/`
**Steps:** Create HTML template → Add email function → Integrate trigger → Test delivery

### Updating Oracle Integration
**Files:** `website/soap_client.py`, `website/backends.py`, `core/constants.py`
**Steps:** Update SOAP client → Modify authentication → Test connection → Update constants

### Adding New API Endpoint
**Files:** `api/views.py`, `api/serializers.py`, `api/urls.py`
**Steps:** Create serializer → Add view → Define URL → Add authentication → Test endpoint

### Modifying User Registration Flow
**Files:** `website/views.py`, `website/forms.py`, `website/templates/website/registration/`
**Steps:** Update form → Modify view logic → Update templates → Test flow → Update emails

---

## 🔧 Development Environment Setup

### Local Development
```bash
# Clone repository
git clone [repository-url]
cd Academy-LMS

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup database
python manage.py migrate
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

### Docker Development
```bash
# Build and run with Docker Compose
docker-compose up --build

# Run migrations in container
docker-compose exec web python manage.py migrate

# Create superuser in container
docker-compose exec web python manage.py createsuperuser
```

---

## 📊 Performance Monitoring

### Key Metrics to Monitor
- **API Response Times**: Check `drf_api_logger` for slow endpoints (>200ms)
- **Database Query Performance**: Monitor query count and execution time
- **Email Delivery**: Check `EmailLog` for failed deliveries
- **External Service Calls**: Monitor `WebServiceLog` for integration issues
- **User Session Activity**: Track login/logout patterns
- **Error Rates**: Monitor Django error logs and exception tracking

### Performance Optimization Areas
- **Database Queries**: Use `select_related()` and `prefetch_related()`
- **Caching**: Implement Redis/Memcached for frequently accessed data
- **Static Files**: Use CDN for static asset delivery
- **API Throttling**: Adjust rate limits based on usage patterns
- **Background Tasks**: Use Celery for heavy processing

---

*Last Updated: 2025-07-07*
*Version: 1.0*
*Maintained by: Academy LMS Development Team*
