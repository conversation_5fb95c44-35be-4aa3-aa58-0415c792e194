# LMS System Change Guide - Go-To Report

## 🎯 Quick Navigation for System Changes

This document provides a quick reference for locating and modifying different aspects of the Academy LMS system.

---

## 📁 File Structure Overview

```
Academy-LMS/
├── project/                    # Main Django project
│   ├── settings.py            # Core configuration
│   ├── urls.py               # Main URL routing
│   └── wsgi.py               # WSGI configuration
├── website/                   # Main application
│   ├── models.py             # Database models
│   ├── views.py              # Main view functions
│   ├── urls.py               # Website URL patterns
│   ├── admin.py              # Admin interface
│   ├── forms.py              # Form definitions
│   ├── middleware.py         # Custom middleware
│   ├── backends.py           # Authentication backends
│   ├── decorators.py         # Access control decorators
│   ├── templates/            # HTML templates
│   └── migrations/           # Database migrations
├── api/                      # REST API application
├── core/                     # Core utilities
├── admins/                   # Admin-specific functionality
└── static/                   # Static files (CSS, JS, images)
```

---

## 🔧 Common Change Scenarios

### 1. Adding New User Types

**Files to Modify:**
- `website/models.py` - Add new UserType entry
- `website/admin.py` - Register admin interface
- `website/views.py` - Add user type-specific views
- `website/urls.py` - Add new URL patterns
- `website/templates/` - Create user-specific templates

**Steps:**
1. Create UserType in database via admin or migration
2. Update authentication logic in `backends.py`
3. Add role-based access in `decorators.py`
4. Create dashboard view in `dashboard_views.py`
5. Add URL routing in `urls.py`

### 2. Adding New Course Features

**Files to Modify:**
- `website/models.py` - Course model modifications
- `website/forms.py` - Course creation/edit forms
- `website/views.py` - Course management views
- `website/templates/website/courses/` - Course templates
- `website/admin.py` - Admin interface updates

**Key Models:**
- `Course` - Main course definition
- `CourseInstance` - Scheduled course instances
- `Session` - Individual course sessions
- `CourseContent` - Course materials

### 3. Modifying Email Notifications

**Files to Modify:**
- `corporate_email_utils.py` - Corporate notifications
- `individual_email_utils.py` - Individual user notifications
- `trainer_email_utils.py` - Trainer notifications
- `GB_email_utils.py` - GB employee notifications
- `otp_email_utils.py` - OTP-related emails

**Email Trigger Locations:**
- Corporate: `website/corporate_views.py`
- Individual: `website/individual_views.py`
- Trainer: `website/views_trainer.py`
- GB Employee: `website/GB_views.py`

### 4. Adding New API Endpoints

**Files to Modify:**
- `api/views.py` - API view functions
- `api/serializers.py` - Data serialization
- `api/urls.py` - API URL patterns
- `website/urls.py` - Include API URLs

**REST Framework Configuration:**
- `project/settings.py` - REST_FRAMEWORK settings
- Authentication classes and permissions

### 5. Database Schema Changes

**Process:**
1. Modify models in `website/models.py`
2. Create migration: `python manage.py makemigrations`
3. Apply migration: `python manage.py migrate`
4. Update admin interface in `website/admin.py`
5. Update forms in `website/forms.py`

### 6. Authentication & Access Control

**Files to Modify:**
- `website/backends.py` - Authentication backends
- `website/middleware.py` - Access control middleware
- `website/decorators.py` - Permission decorators
- `project/settings.py` - Authentication settings

**Key Components:**
- `EmailOrUsernameModelBackend` - External user auth
- `OracleEmployeeBackend` - GB employee auth
- `ForcePasswordResetMiddleware` - Password enforcement
- `AdminITGroupMiddleware` - Admin access control

### 7. Frontend/Template Changes

**Template Structure:**
- `website/templates/website/base.html` - Main layout
- `website/templates/website/dashboard/` - Dashboard templates
- `website/templates/website/courses/` - Course templates
- `website/templates/website/calendar/` - Calendar views

**Static Files:**
- `static/css/` - Stylesheets
- `static/js/` - JavaScript files
- `static/images/` - Image assets

### 8. Integration Updates

**Oracle EBS Integration:**
- `website/soap_client.py` - SOAP API client
- `website/models.py` - GB_Employee model
- `website/otp_views.py` - OTP authentication
- `core/constants.py` - API endpoints

**Payment Integration:**
- `core/paymob.py` - Payment gateway
- `website/views.py` - Payment processing
- `core/constants.py` - Payment configuration

**SMS Integration:**
- `core/vodafone.py` - SMS service
- `website/otp_views.py` - OTP delivery
- `core/constants.py` - SMS configuration

---

## 🗄️ Database Model Quick Reference

### Core Models Location: `website/models.py`

**User Management:**
- `CustomUser` - Main user model
- `UserType` - User role definitions
- `GB_Employee` - Oracle employee sync

**Learning Management:**
- `Course` - Course definitions
- `CourseInstance` - Scheduled courses
- `Session` - Individual sessions
- `Reservation` - User enrollments
- `Attendance` - Session attendance

**Resource Management:**
- `Room` - Training rooms
- `Equipment` - Training equipment
- `Trainer` - Instructor profiles
- `RoomAvailability` - Room scheduling
- `TrainerAvailability` - Trainer scheduling

**Assessment:**
- `Questionnaire` - Course assessments
- `QuestionnaireResponse` - User responses
- `Survey` - Feedback surveys
- `SurveyResponse` - Survey responses

**Corporate Management:**
- `Corporate` - Company profiles
- `CorporateAdmin` - Corporate administrators
- `CorporateAdminRequest` - Course requests

---

## 🔗 URL Pattern Quick Reference

### Main URLs: `project/urls.py`
- `/dzJAMvwB/` - Admin panel
- `/api/v1/` - REST API
- `/api/otp/` - OTP authentication
- `/` - Main website (i18n)

### Website URLs: `website/urls.py`
- `/dashboard-redirect/` - Smart dashboard routing
- `/courses/` - Course management
- `/calendar/` - Calendar views
- `/my-reservations/` - User reservations
- `/trainer/` - Trainer-specific views
- `/corporate/` - Corporate admin views
- `/GB_supervisor/` - GB supervisor views

---

## ⚙️ Configuration Files

### Main Settings: `project/settings.py`
- Database configuration
- Email settings
- Authentication backends
- Middleware configuration
- Static/media file settings
- Internationalization settings

### Environment Variables (.env)
- `SECRET_KEY` - Django secret key
- `DEBUG` - Debug mode flag
- `SQL_*` - Database credentials
- `EMAIL_*` - Email server settings
- `ORACLE_*` - Oracle API settings
- `PAYMOB_*` - Payment gateway settings
- `VODAFONE_*` - SMS service settings

---

## 🚀 Deployment & Maintenance

### Docker Configuration
- `Dockerfile` - Application container
- `docker-compose.yaml` - Development setup
- `docker-compose-prod.yaml` - Production setup
- `nginx/` - Nginx configuration

### Database Management
- `manage.py migrate` - Apply migrations
- `manage.py makemigrations` - Create migrations
- `manage.py collectstatic` - Collect static files
- `manage.py createsuperuser` - Create admin user

### Cron Jobs: `core/crons.py`
- Log purging (daily at midnight)
- Configured in `project/settings.py`

---

## 📝 Change Log Template

When making changes, document them using this template:

```markdown
## Change Log Entry - [Date]

### Change Type: [Feature/Bug Fix/Enhancement/Configuration]

### Files Modified:
- `path/to/file1.py` - Description of changes
- `path/to/file2.html` - Description of changes

### Database Changes:
- [ ] New migrations created
- [ ] Migration applied
- [ ] Admin interface updated

### Testing:
- [ ] Unit tests updated
- [ ] Manual testing completed
- [ ] User acceptance testing

### Deployment Notes:
- [ ] Environment variables updated
- [ ] Static files collected
- [ ] Cache cleared
- [ ] Services restarted

### Rollback Plan:
- Steps to revert changes if needed
```

---

## 🔍 Debugging & Troubleshooting

### Log Files
- Django logs: Check `DEBUG` setting and console output
- API logs: `drf_api_logger` database table
- Email logs: `EmailLog` model in database
- External service logs: `WebServiceLog` model

### Common Issues
- **Authentication problems**: Check `website/backends.py`
- **Permission errors**: Check `website/decorators.py` and middleware
- **Email not sending**: Check email backend configuration
- **Oracle integration**: Check SOAP client and credentials
- **Payment issues**: Check Paymob configuration and logs

---

## 📋 Quick Reference Checklists

### Pre-Change Checklist
- [ ] Backup current database
- [ ] Document current system state
- [ ] Identify all affected components
- [ ] Plan rollback strategy
- [ ] Test in development environment

### Post-Change Checklist
- [ ] Run all migrations
- [ ] Update admin interface if needed
- [ ] Test all affected user flows
- [ ] Update documentation
- [ ] Monitor system logs for errors
- [ ] Verify email notifications work
- [ ] Test API endpoints if modified
- [ ] Check integration points (Oracle, Paymob, SMS)

### Emergency Rollback Checklist
- [ ] Stop application services
- [ ] Restore database backup
- [ ] Revert code changes
- [ ] Restart services
- [ ] Verify system functionality
- [ ] Notify stakeholders

---

## 🎯 Specific Change Scenarios

### Adding New Dashboard Widget
**Files:** `website/templates/website/dashboard/`, `website/views.py`
**Steps:** Create widget template → Add view logic → Update dashboard template → Test

### Modifying Course Enrollment Logic
**Files:** `website/views.py`, `website/models.py`, `website/forms.py`
**Steps:** Update model → Create migration → Modify view → Update form → Test workflow

### Adding New Email Template
**Files:** `*_email_utils.py`, `website/templates/emails/`
**Steps:** Create HTML template → Add email function → Integrate trigger → Test delivery

### Updating Oracle Integration
**Files:** `website/soap_client.py`, `website/backends.py`, `core/constants.py`
**Steps:** Update SOAP client → Modify authentication → Test connection → Update constants

### Adding New API Endpoint
**Files:** `api/views.py`, `api/serializers.py`, `api/urls.py`
**Steps:** Create serializer → Add view → Define URL → Add authentication → Test endpoint

### Modifying User Registration Flow
**Files:** `website/views.py`, `website/forms.py`, `website/templates/website/registration/`
**Steps:** Update form → Modify view logic → Update templates → Test flow → Update emails

---

## 🔧 Development Environment Setup

### Local Development
```bash
# Clone repository
git clone [repository-url]
cd Academy-LMS

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup database
python manage.py migrate
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

### Docker Development
```bash
# Build and run with Docker Compose
docker-compose up --build

# Run migrations in container
docker-compose exec web python manage.py migrate

# Create superuser in container
docker-compose exec web python manage.py createsuperuser
```

---

## 📊 Performance Monitoring

### Key Metrics to Monitor
- **API Response Times**: Check `drf_api_logger` for slow endpoints (>200ms)
- **Database Query Performance**: Monitor query count and execution time
- **Email Delivery**: Check `EmailLog` for failed deliveries
- **External Service Calls**: Monitor `WebServiceLog` for integration issues
- **User Session Activity**: Track login/logout patterns
- **Error Rates**: Monitor Django error logs and exception tracking

### Performance Optimization Areas
- **Database Queries**: Use `select_related()` and `prefetch_related()`
- **Caching**: Implement Redis/Memcached for frequently accessed data
- **Static Files**: Use CDN for static asset delivery
- **API Throttling**: Adjust rate limits based on usage patterns
- **Background Tasks**: Use Celery for heavy processing

---

*Last Updated: 2025-07-07*
*Version: 1.0*
*Maintained by: Academy LMS Development Team*
