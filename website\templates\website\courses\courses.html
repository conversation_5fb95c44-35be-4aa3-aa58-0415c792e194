{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Courses" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header with Action Buttons -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            {% trans "Courses" %}
        </h1>
        
        <div class="flex items-center space-x-4">
            <div class="flex bg-white/5 rounded-md p-1">
                <button class="group relative p-2 rounded text-white bg-white/10 view-toggle active" data-view="grid" title="{% trans 'Grid View' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                    </svg>
                </button>
                <button class="group relative p-2 rounded text-white/70 hover:text-white hover:bg-white/10 view-toggle" data-view="table" title="{% trans 'Table View' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                </button>
            </div>
            {% if is_trainer or request.user.user_type.code == 'SUPER_ADMIN' %}
            <a href="{% url 'website:create_course' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                {% trans "Create Course" %}
            </a>
            {% endif %}
            {% if is_supervisor or request.user.user_type.code == 'SUPER_ADMIN' %}
            <a href="{% url 'website:assign_course_to_trainers' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary hover:bg-secondary/90 ml-3">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                </svg>
                {% trans "Assign Course" %}
            </a>
            {% endif %}
            {% if is_corporate_admin %}
            <a href="{% url 'website:request_new_course' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 ml-3">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                </svg>
                {% trans "Request Course Not On System" %}
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 mb-8">
        <div class="flex flex-wrap items-center gap-6">
            <!-- Search -->
            <div class="flex-1 min-w-[300px]">
                <div class="relative">
                    <input type="text" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 pl-10 pr-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="{% trans 'Search courses...' %}">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Category Filter Group -->
            <div class="flex items-center gap-2">
                <span class="text-white/70 text-sm flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                    </svg>
                    {% trans "Category:" %}
                </span>
                <div class="flex bg-white/5 rounded-md p-1 gap-1">
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Categories' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Programming' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Design' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Business' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Type Filter Group -->
            <div class="flex items-center gap-2">
                <span class="text-white/70 text-sm flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
                    </svg>
                    {% trans "Type:" %}
                </span>
                <div class="flex bg-white/5 rounded-md p-1 gap-1">
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Types' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Workshop' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Room' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Lab' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Online' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Level Filter Group -->
            <div class="flex items-center gap-2">
                <span class="text-white/70 text-sm flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    {% trans "Level:" %}
                </span>
                <div class="flex bg-white/5 rounded-md p-1 gap-1">
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Levels' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Beginner' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Intermediate' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'Advanced' %}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Grid View -->
    <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for category, courses in course_categories.items %}
            <!-- Category Header -->
            <div class="col-span-full">
                <h2 class="text-xl font-semibold text-white mb-4">{{ category }}</h2>
            </div>

            {% for course in courses %}
            <div class="bg-white/5 backdrop-blur-md rounded-lg overflow-hidden border border-white/10 hover:border-primary transition-colors">
                <div class="relative">
                    <div class="w-full h-48 bg-white/5 flex items-center justify-center overflow-hidden">
                        {% if course.image %}
                            <img src="{{ course.image.url }}" alt="{{ course.name_en }}" class="w-full h-full object-cover">
                        {% elif course.icon_svg %}
                            {{ course.icon_svg|safe }}
                        {% else %}
                            <svg class="w-24 h-24 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        {% endif %}
                    </div>
                    <div class="absolute top-4 right-4 bg-primary/90 text-white text-xs font-semibold px-2 py-1 rounded">
                        {{ course.get_category_display }}
                    </div>
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-white mb-2">{{ course.name_en }}</h3>
                    <p class="text-white/70 text-sm mb-4">{{ course.description_en|truncatewords:20 }}</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-white/70 text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            {{ course.get_location_display }}
                        </div>
                        {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                        <div class="flex items-center text-white/70 text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            {{ course.instance_count }} {% trans "Available Instances" %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-between items-center">
                        <a href="{% url 'website:course_detail' course.course_id %}" class="inline-flex items-center px-4 py-2 border border-white/20 rounded-md shadow-sm text-sm font-medium text-white bg-white/10 hover:bg-white/20 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            {% trans "View Details" %}
                        </a>
                        {% if course.instances %}
                            {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                <!-- Check if user is enrolled in this course -->
                                {% with user_enrolled_in_course=False user_reservation=None %}
                                    {% for reservation in request.user.reservation_set.all %}
                                        {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' %}
                                            {% with user_enrolled_in_course=True user_reservation=reservation %}{% endwith %}
                                        {% endif %}
                                    {% endfor %}

                                    {% if not user_enrolled_in_course %}
                                        <!-- Show Enroll button for non-enrolled users -->
                                        <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                            </svg>
                                            {% trans "Enroll" %}
                                        </button>
                                    {% else %}
                                        <!-- Show enrolled user actions -->
                                        <div class="flex space-x-2">
                                            <!-- Get user's reservation for this course -->
                                            {% with user_reservation=None %}
                                                {% for reservation in request.user.reservation_set.all %}
                                                    {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' and not user_reservation %}
                                                        {% with user_reservation=reservation %}{% endwith %}
                                                    {% endif %}
                                                {% endfor %}

                                                {% if user_reservation %}
                                                    <!-- Pay Now Button (if waiting to pay) -->
                                                    {% if user_reservation.status == 'WAITING_TO_PAY' %}
                                                    <form method="POST" action="{% url 'website:check_before_payment' user_reservation.reservation_id %}" class="inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                            </svg>
                                                            {% trans "Pay Now" %}
                                                        </button>
                                                    </form>
                                                    {% endif %}

                                                    <!-- Cancel Button -->
                                                    {% if user_reservation.status == 'UPCOMING' or user_reservation.status == 'WAITING_TO_PAY' or user_reservation.status == 'WAITING_LIST' %}
                                                    <button onclick="openCancellationModal('{{ user_reservation.reservation_id }}')" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-red-600 hover:bg-red-700 transition-colors">
                                                        <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                        </svg>
                                                        {% trans "Cancel" %}
                                                    </button>
                                                    {% endif %}
                                                {% endif %}
                                            {% endwith %}
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% empty %}
            <div class="col-span-full text-center text-white/70 py-8">
                {% trans "No courses available." %}
            </div>
        {% endfor %}
    </div>

    <!-- Table View (Hidden by Default) -->
    <div id="table-view" class="hidden">
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Category" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Location" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% if is_external_individual or is_gb_user %}
                                {% trans "Available Instances" %}
                                {% else %}
                                {% trans "Status" %}
                                {% endif %}
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">{% trans "Actions" %}</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        {% for category, courses in course_categories.items %}
                            {% for course in courses %}
                            <tr class="hover:bg-white/10">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-white/5 rounded-lg flex items-center justify-center">
                                            {% if course.icon_svg %}
                                                {{ course.icon_svg|safe }}
                                            {% else %}
                                                <svg class="h-6 w-6 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                                </svg>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-white">{{ course.name_en }}</div>
                                            <div class="text-sm text-white/70">{{ course.description_en|truncatechars:50 }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary/30 text-white">
                                        {{ course.get_category_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/70">
                                    {{ course.get_location_display }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/70">
                                    {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                        {{ course.instance_count }} {% trans "Available" %}
                                    {% else %}
                                        --
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <a href="{% url 'website:course_detail' course.course_id %}" class="inline-flex items-center px-3 py-1.5 border border-white/20 rounded-md shadow-sm text-xs font-medium text-white bg-white/10 hover:bg-white/20 transition-colors">
                                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            {% trans "View Details" %}
                                        </a>
                                        {% if course.instances %}
                                            {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                                <!-- Check if user is enrolled in this course -->
                                                {% with user_enrolled_in_course=False user_reservation=None %}
                                                    {% for reservation in request.user.reservation_set.all %}
                                                        {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' %}
                                                            {% with user_enrolled_in_course=True user_reservation=reservation %}{% endwith %}
                                                        {% endif %}
                                                    {% endfor %}

                                                    {% if not user_enrolled_in_course %}
                                                        <!-- Show Enroll button for non-enrolled users -->
                                                        <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                                            </svg>
                                                            {% trans "Enroll" %}
                                                        </button>
                                                    {% else %}
                                                        <!-- Show enrolled user actions -->
                                                        <div class="flex space-x-1">
                                                            <!-- Get user's reservation for this course -->
                                                            {% with user_reservation=None %}
                                                                {% for reservation in request.user.reservation_set.all %}
                                                                    {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' and not user_reservation %}
                                                                        {% with user_reservation=reservation %}{% endwith %}
                                                                    {% endif %}
                                                                {% endfor %}

                                                                {% if user_reservation %}
                                                                    <!-- Pay Now Button (if waiting to pay) -->
                                                                    {% if user_reservation.status == 'WAITING_TO_PAY' %}
                                                                    <form method="POST" action="{% url 'website:check_before_payment' user_reservation.reservation_id %}" class="inline">
                                                                        {% csrf_token %}
                                                                        <button type="submit" class="inline-flex items-center px-2 py-1 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                                            </svg>
                                                                            {% trans "Pay" %}
                                                                        </button>
                                                                    </form>
                                                                    {% endif %}

                                                                    <!-- Cancel Button -->
                                                                    {% if user_reservation.status == 'UPCOMING' or user_reservation.status == 'WAITING_TO_PAY' or user_reservation.status == 'WAITING_LIST' %}
                                                                    <button onclick="openCancellationModal('{{ user_reservation.reservation_id }}')" class="inline-flex items-center px-2 py-1 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-red-600 hover:bg-red-700 transition-colors">
                                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                                        </svg>
                                                                        {% trans "Cancel" %}
                                                                    </button>
                                                                    {% endif %}
                                                                {% endif %}
                                                            {% endwith %}
                                                        </div>
                                                    {% endif %}
                                                {% endwith %}
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Enroll Modal -->
{% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
<div id="enroll-modal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold text-white mb-4">{% trans "Choose Course Instance" %}</h3>
        <div class="mb-6">
            <select id="instance-select" class="w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>
            </select>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="closeEnrollModal()" class="px-4 py-2 text-white/70 hover:text-white">
                {% trans "Cancel" %}
            </button>
            <button onclick="enrollInInstance()" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                {% trans "Enroll" %}
            </button>
        </div>
    </div>
</div>

<script>
let enrollUrl = '';
{% if is_gb_user %}
    enrollUrl = "{% url 'website:gb_enroll_in_instance' 999999 %}";
{% elif is_external_individual %}
    enrollUrl = "{% url 'website:enroll_in_instance' 999999 %}";
{% endif %}

let selectedInstanceId = null;
let courseIdForModal = null;
let courseInstances = {};

{% for category, courses in course_categories.items %}
    {% for course in courses %}
        courseInstances['{{ course.course_id }}'] = [
            {% for instance in course.instances %}
            {
                id: '{{ instance.instance_id }}',
                startDate: '{{ instance.start_date|date:"M d, Y" }}',
                startDateRaw: '{{ instance.start_date|date:"Y-m-d" }}',
                endDate: '{{ instance.end_date|date:"M d, Y" }}',
                sessionCount: {{ instance.sessions.count }},
                capacity: {{ instance.capacity }},
                availableSeats: {{ instance.available_seats }},
                waitingListCount: {{ instance.waiting_list_count }},
                courseStarted: {% if instance.time_until_deadline == "Deadline has passed" %}true{% else %}false{% endif %}
            },
            {% endfor %}
        ];
    {% endfor %}
{% endfor %}

// Track user enrollments
let userEnrollments = [
    {% for reservation in request.user.reservation_set.all %}
        {% if reservation.course_instance and reservation.status != 'CANCELLED' %}
            "{{ reservation.course_instance.instance_id }}",
        {% endif %}
    {% endfor %}
];

// Check if start date has passed
function hasStartDatePassed(startDateRaw) {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day
    const startDate = new Date(startDateRaw);
    return today >= startDate;
}

function showEnrollModal(courseId) {
    const instances = courseInstances[courseId];
    const instanceSelect = document.getElementById('instance-select');
    instanceSelect.innerHTML = '<option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>';

    instances.forEach(instance => {
        // Check if user is already enrolled (and not cancelled)
        const isEnrolled = userEnrollments.includes(instance.id);
        const startDatePassed = hasStartDatePassed(instance.startDateRaw);
        
        // Skip instances where user is already enrolled or start date has passed
        if (isEnrolled || startDatePassed) {
            return; // Don't add this instance to the dropdown
        }
        
        const option = document.createElement('option');
        option.value = instance.id;
        option.className = 'bg-gray-800';
        
        if (instance.availableSeats <= 0) {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, {% trans "Fully Booked" %})`;
            if (instance.waitingListCount > 0) {
                option.textContent += ` - {% trans "Waiting List" %}: ${instance.waitingListCount}`;
            }
        } else {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, ${instance.availableSeats} {% trans "seats left" %})`;
        }
        instanceSelect.appendChild(option);
    });

    // Reset selection
    selectedInstanceId = null;
    instanceSelect.value = '';

    // Add change event listener
    instanceSelect.addEventListener('change', (e) => {
        selectedInstanceId = e.target.value;
    });

    document.getElementById('enroll-modal').classList.remove('hidden');
    document.getElementById('enroll-modal').classList.add('flex');
}

// Function to show notification in the modal
function showModalNotification(message, type) {
    // Remove any existing notification
    const existingNotification = document.getElementById('modal-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create new notification
    const notification = document.createElement('div');
    notification.id = 'modal-notification';
    
    // Set styles based on type
    if (type === 'error') {
        notification.className = 'bg-red-500/10 border-red-500/30 text-red-400 p-3 rounded-md border mb-4';
    } else if (type === 'warning') {
        notification.className = 'bg-amber-500/10 border-amber-500/30 text-amber-400 p-3 rounded-md border mb-4';
    } else if (type === 'success') {
        notification.className = 'bg-green-500/10 border-green-500/30 text-green-400 p-3 rounded-md border mb-4';
    }
    
    notification.textContent = message;
    
    // Insert at the top of the modal content
    const modalContent = document.querySelector('#enroll-modal > div');
    modalContent.insertBefore(notification, modalContent.firstChild);
    
    // Set timeout based on notification type
    let timeout = 5000; // Default: 5 seconds
    
    if (type === 'success') {
        timeout = 5000; // 5 seconds for success
    } else if (type === 'warning') {
        timeout = 7000; // 7 seconds for warnings
    } else if (type === 'error') {
        timeout = 10000; // 10 seconds for errors
    }
    
    // Auto-dismiss all notifications after their timeout
    setTimeout(() => {
        notification.style.transition = 'opacity 0.5s ease';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 500);
    }, timeout);
}

function closeEnrollModal() {
    document.getElementById('enroll-modal').classList.add('hidden');
    document.getElementById('enroll-modal').classList.remove('flex');
    selectedInstanceId = null;
    
    // Remove any notifications
    const notification = document.getElementById('modal-notification');
    if (notification) {
        notification.remove();
    }
}

function enrollInInstance() {
    if (!selectedInstanceId) {
        showModalNotification('{% trans "Please select an instance" %}', 'warning');
        return;
    }
    
    // Find which course this instance belongs to
    const courseId = Object.keys(courseInstances).find(id => 
        courseInstances[id].some(instance => instance.id === selectedInstanceId)
    );
    
    if (!courseId) {
        showModalNotification('{% trans "Invalid course instance" %}', 'error');
        return;
    }
    
    const instance = courseInstances[courseId].find(instance => instance.id === selectedInstanceId);
    
    // Check if already enrolled
    if (userEnrollments.includes(selectedInstanceId)) {
        showModalNotification('{% trans "You are already enrolled in this course instance" %}', 'warning');
        return;
    }
    
    // Check if start date has passed
    if (instance && hasStartDatePassed(instance.startDateRaw)) {
        showModalNotification('{% trans "Enrollment for this course instance is closed because the course has already started." %}', 'error');
        return;
    }
    
    if (instance && instance.availableSeats <= 0) {
        // Ask if they want to join the waiting list instead
        if (confirm('{% trans "This course instance is fully booked. Would you like to join the waiting list instead?" %}')) {
            // Redirect to enrollment with waitlist parameter
            window.location.href = enrollUrl.replace('999999', selectedInstanceId) + '?waitlist=yes';
            return;
        } else {
            return; // User declined to join waitlist
        }
    }
    
    // All checks passed, proceed with enrollment
    window.location.href = enrollUrl.replace('999999', selectedInstanceId);
}

// Cancellation Modal Functions
function openCancellationModal(reservationId) {
    document.getElementById('cancellation_reservation_id').value = reservationId;
    document.getElementById('cancellationConfirmModal').classList.remove('hidden');
}

function closeCancellationModal() {
    document.getElementById('cancellationConfirmModal').classList.add('hidden');
}
</script>

<!-- Cancellation Confirmation Modal -->
<div id="cancellationConfirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-white">{% trans "Cancel Reservation" %}</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-300">{% trans "Are you sure you want to cancel this reservation? This action cannot be undone." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "Please note that cancellation policies may apply. You can cancel your reservation up to the cancellation deadline without penalty." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
                        </div>
                        <form id="cancellationForm" method="POST" action="{% url 'website:user_reservations' %}">
                            {% csrf_token %}
                            <input type="hidden" id="cancellation_reservation_id" name="reservation_id" value="">
                            <input type="hidden" name="cancel_reservation" value="true">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="document.getElementById('cancellationForm').submit();" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm Cancel" %}
                </button>
                <button type="button" onclick="closeCancellationModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const gridView = document.getElementById('grid-view');
        const tableView = document.getElementById('table-view');
        const viewToggles = document.querySelectorAll('.view-toggle');

        viewToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const view = this.getAttribute('data-view');
                
                // Remove active class from all toggles
                viewToggles.forEach(t => {
                    t.classList.remove('active', 'bg-white/10');
                    t.classList.add('text-white/70', 'hover:text-white', 'hover:bg-white/10');
                });
                
                // Add active class to clicked toggle
                this.classList.add('active', 'bg-white/10', 'text-white');
                this.classList.remove('text-white/70', 'hover:text-white', 'hover:bg-white/10');
                
                // Show selected view
                if (view === 'grid') {
                    gridView.classList.remove('hidden');
                    tableView.classList.add('hidden');
                } else {
                    gridView.classList.add('hidden');
                    tableView.classList.remove('hidden');
                }
            });
        });
    });
</script>
{% endblock %} 