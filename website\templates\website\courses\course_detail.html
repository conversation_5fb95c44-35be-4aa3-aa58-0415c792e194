{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ course.name_en }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <script>
            // Auto-dismiss Django flash messages
            document.addEventListener('DOMContentLoaded', function() {
                const messages = document.querySelectorAll('.notification-message');
                messages.forEach(message => {
                    const type = message.getAttribute('data-type');
                    let timeout = 5000; // Default 5 seconds
                    
                    // Different timeouts based on message type
                    if (type === 'success') {
                        timeout = 5000; // 5 seconds for success
                    } else if (type === 'warning') {
                        timeout = 7000; // 7 seconds for warnings
                    } else if (type === 'error') {
                        timeout = 10000; // 10 seconds for errors
                    }
                    
                    setTimeout(() => {
                        message.style.transition = 'opacity 0.5s ease';
                        message.style.opacity = '0';
                        setTimeout(() => {
                            message.remove();
                            
                            // If no more messages, remove the container
                            if (document.querySelectorAll('.notification-message').length === 0) {
                                const container = document.getElementById('notifications-container');
                                if (container) container.remove();
                            }
                        }, 500);
                    }, timeout);
                });
            });
        </script>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:courses' %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{{ course.name_en }}</h1>
        </div>

        <!-- Course Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Course Icon and Category -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg overflow-hidden border border-white/10">
                    <div class="relative">
                        <div class="w-full h-64 bg-white/5 flex items-center justify-center overflow-hidden">
                            {% if course.image %}
                                <img src="{{ course.image.url }}" alt="{{ course.name_en }}" class="w-full h-full object-cover">
                            {% elif course.icon_svg %}
                                {{ course.icon_svg|safe }}
                            {% else %}
                                <svg class="w-32 h-32 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            {% endif %}
                        </div>
                        <div class="absolute top-4 right-4 bg-primary/90 text-white text-xs font-semibold px-2 py-1 rounded">
                            {{ course.get_category_display }}
                        </div>
                    </div>
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-white mb-4">{% trans "Description" %}</h2>
                        <div class="prose prose-invert max-w-none">
                            <p class="text-white/70">{{ course.description_en }}</p>
                        </div>
                    </div>
                </div>

                <!-- Prerequisites -->
                {% if course.prerequisite %}
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Prerequisites" %}</h2>
                    <p class="text-white/70">{{ course.prerequisite }}</p>
                </div>
                {% endif %}

                <!-- Course Content -->
                {% if course.contents.exists and has_paid or can_manage or is_admin or user.user_type.code == 'SUPER_ADMIN' %}
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Course Content" %}</h2>
                    <div class="space-y-3">
                        {% for content in course.contents.all %}
                        {% if content.is_active %}
                        <div class="flex justify-between items-center p-3 border border-white/10 rounded-md hover:bg-white/5">
                            <div>
                                <h3 class="text-white font-medium">{{ content.title }}</h3>
                                <p class="text-white/70 text-sm">{{ content.get_content_type_display }}</p>
                                {% if content.description %}
                                <p class="text-white/70 text-sm mt-1">{{ content.description }}</p>
                                {% endif %}
                            </div>
                            <a href="{% url 'website:download_course_content' course.course_id content.content_id %}" class="inline-flex items-center justify-center bg-primary text-white px-3 py-1 rounded-md hover:bg-primary/90 text-sm">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                {% trans "Download" %}
                            </a>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% elif course.contents.exists %}
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Course Content" %}</h2>
                    <div class="p-4 bg-amber-500/10 border border-amber-500/30 rounded-md">
                        <div class="flex items-start">
                            <svg class="h-5 w-5 text-amber-400 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            {% if has_upcoming_reservation %}
                            <p class="text-sm text-amber-400">{% trans "Course content will be available when the course starts." %}</p>
                            {% else %}
                            <p class="text-sm text-amber-400">{% trans "You need to complete your payment to access course content." %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Required Equipment -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Required Equipment" %}</h2>
                    {% if course.equipment_categories %}
                        <ul class="list-disc list-inside text-white/70 pl-4">
                            {% for category_id, category_data in course.equipment_categories.items %}
                                <li>{{ category_data.name }} ({{ category_data.quantity }} {% if category_data.quantity == 1 %}item{% else %}items{% endif %})</li>
                            {% endfor %}
                        </ul>
                    {% elif course.required_equipment.all %}
                        <!-- For backwards compatibility with existing courses -->
                        <ul class="list-disc list-inside text-white/70 pl-4">
                            {% for equipment in course.required_equipment.all %}
                                <li>{{ equipment.name }}</li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-white/70">{% trans "No specific equipment required" %}</p>
                    {% endif %}
                </div>

                <!-- Session Room Types -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Session Room Types" %}</h2>
                    
                    {% if session_room_types %}
                        <ul class="list-disc list-inside text-white/70 pl-4">
                            {% for session in session_room_types %}
                                <li>
                                    {% trans "Session" %} {{ session.session_num }}: 
                                    {% if session.found %}
                                        <span class="text-primary">{{ session.room_type_name }}</span>
                                    {% else %}
                                        <span class="text-amber-400">{{ session.room_type_name }}</span>
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ul>
                    {% elif course.session_room_types %}
                        <ul class="list-disc list-inside text-white/70 pl-4">
                            {% for session_num, room_type_id in course.session_room_types.items %}
                                <li>{% trans "Session" %} {{ session_num }}: {% trans "Room Type" %} #{{ room_type_id }}</li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-white/70">{% trans "No specific room types required" %}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Course Details -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Course Details" %}</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-white/70">{% trans "Location" %}</span>
                            <span class="text-white">{{ course.get_location_display }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-white/70">{% trans "Session Type" %}</span>
                            <span class="text-white">{{ course.get_session_type_display }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-white/70">{% trans "Number of Sessions" %}</span>
                            <span class="text-white">{{ course.num_of_sessions }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-white/70">{% trans "Capacity" %}</span>
                            <span class="text-white">{{ course.capacity }} {% trans "seats" %}</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    {% if is_trainer and can_manage or user.user_type.code == 'SUPER_ADMIN' %}
                        <!-- Trainer Management Actions -->
                        {% if course.status == 'CANCELLED' %}
                            <form method="post" action="{% url 'website:reactivate_course' course.course_id %}">
                                {% csrf_token %}
                                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md mb-3">
                                    {% trans "Reactivate Course" %}
                                </button>
                            </form>
                            <form method="post" action="{% url 'website:duplicate_course' course.course_id %}">
                                {% csrf_token %}
                                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md mb-3">
                                    {% trans "Duplicate Course" %}
                                </button>
                            </form>
                            <form method="post" action="{% url 'website:delete_course' course.course_id %}" onsubmit="return confirm('{% trans "Are you sure you want to delete this course? This action cannot be undone." %}');">
                                {% csrf_token %}
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md mb-3">
                                    {% trans "Delete Course" %}
                                </button>
                            </form>
                        {% else %}
                            <a href="{% url 'website:edit_course' course.course_id %}" class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md mb-3 block text-center">
                                {% trans "Edit Course" %}
                            </a>
                            {% if is_trainer and can_manage %}
                            <a href="{% url 'website:course_content_list' course.course_id %}" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md mb-3 block text-center">
                                {% trans "Manage Content" %}
                            </a>
                            
                            <form method="post" action="{% url 'website:duplicate_course' course.course_id %}">
                                {% csrf_token %}
                                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md mb-3">
                                    {% trans "Duplicate Course" %}
                                </button>
                            </form>
                            {% endif %}
                            <a href="{% url 'website:cancel_course' course.course_id %}" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md mb-3 block text-center">
                                {% trans "Cancel Course" %}
                            </a>
                        {% endif %}
                    {% endif %}

                    <!-- User Actions -->
                    {% if is_external_individual %}
                        {% if course_instances %}
                            <!-- Check if user is already enrolled in any instance of this course -->
                            {% with user_enrolled_in_course=False user_reservation=None %}
                                {% for reservation in request.user.reservation_set.all %}
                                    {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' %}
                                        {% with user_enrolled_in_course=True user_reservation=reservation %}{% endwith %}
                                    {% endif %}
                                {% endfor %}

                                {% if not user_enrolled_in_course %}
                                    <!-- Show enrollment section for non-enrolled users -->
                                    <h3 class="text-lg font-medium text-white mb-4">{% trans "Available Sessions" %}</h3>
                                    <div class="space-y-3 mb-4">
                                        {% for instance in course_instances %}
                                        <div class="p-3 border border-white/10 rounded-md hover:bg-white/5">
                                            <div class="flex justify-between items-center mb-2">
                                                <div class="text-sm font-medium text-white">{{ instance.session_count }} {% trans "sessions" %}</div>
                                                <div class="text-xs text-white/70">ID: {{ instance.instance_id }}</div>
                                            </div>
                                            <div class="flex justify-between text-sm text-white/70">
                                                <div>{% trans "Start:" %} {{ instance.start_date|date:"M d, Y" }}</div>
                                                <div>{% trans "End:" %} {{ instance.end_date|date:"M d, Y" }}</div>
                                            </div>
                                            <div class="flex items-center mt-1 text-sm text-white/70">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                                                </svg>
                                                {% trans "Capacity:" %} {{ instance.capacity }} {% trans "seats" %}
                                                <span class="mx-2">•</span>
                                                {% trans "Available:" %}
                                                <span class="{% if instance.available_seats > 0 %}text-green-400{% else %}text-red-400{% endif %} ml-1">
                                                    {{ instance.available_seats }} {% trans "seats" %}
                                                </span>
                                                {% if instance.available_seats <= 0 %}
                                                <span class="ml-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">{% trans "Fully Booked" %}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <button onclick="showEnrollModal()" class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md mb-3">
                                        {% trans "Enroll" %}
                                    </button>
                                {% else %}
                                    <!-- Show enrolled user actions -->
                                    <h3 class="text-lg font-medium text-white mb-4">{% trans "Course Actions" %}</h3>
                                    <div class="space-y-3">
                                        <!-- Get user's reservation for this course -->
                                        {% with user_reservation=None %}
                                            {% for reservation in request.user.reservation_set.all %}
                                                {% if reservation.course_instance.course.course_id == course.course_id and reservation.status != 'CANCELLED' and not user_reservation %}
                                                    {% with user_reservation=reservation %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}

                                            {% if user_reservation %}
                                                <!-- Pay Now Button (if waiting to pay) -->
                                                {% if user_reservation.status == 'WAITING_TO_PAY' %}
                                                <form method="POST" action="{% url 'website:check_before_payment' user_reservation.reservation_id %}" class="w-full">
                                                    {% csrf_token %}
                                                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md mb-3 inline-flex items-center justify-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                        </svg>
                                                        {% trans "Pay Now" %}
                                                    </button>
                                                </form>
                                                {% endif %}

                                                <!-- Cancel Button -->
                                                {% if user_reservation.status == 'UPCOMING' or user_reservation.status == 'WAITING_TO_PAY' or user_reservation.status == 'WAITING_LIST' %}
                                                <button onclick="openCancellationModal('{{ user_reservation.reservation_id }}')" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md mb-3 inline-flex items-center justify-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                    </svg>
                                                    {% trans "Cancel Reservation" %}
                                                </button>
                                                {% endif %}

                                                <!-- Emergency Cancel Button (will be shown via JavaScript if deadline passed) -->
                                                <button id="emergency-cancel-btn-{{ user_reservation.reservation_id }}" onclick="openEmergencyCancellationModal('{{ user_reservation.reservation_id }}')" class="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-md mb-3 inline-flex items-center justify-center hidden">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                    </svg>
                                                    {% trans "Emergency Cancel" %}
                                                </button>
                                            {% endif %}
                                        {% endwith %}
                                    </div>
                                {% endif %}
                            {% endwith %}
                        {% else %}
                            <div class="p-4 bg-amber-500/10 border border-amber-500/30 rounded-md mb-4">
                                <div class="flex items-start">
                                    <svg class="h-5 w-5 text-amber-400 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p class="text-sm text-amber-400">{% trans "No upcoming sessions available for this course. Check back later." %}</p>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        {% if course.status != 'CANCELLED' %}
                            {% if user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN' %}
                            <a href="{% url 'website:course_request_form' course.course_id %}" class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md mb-3 block text-center">
                                {% trans "Request Course" %}
                            </a>
                            {% else %}
                            <button class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md mb-3">
                                {% trans "Enroll Now" %}
                            </button>
                            {% endif %}
                        {% else %}
                            <div class="w-full bg-red-600/10 text-red-500 font-medium py-2 px-4 rounded-md mb-3 text-center">
                                {% trans "Course Cancelled" %}
                            </div>
                        {% endif %}
                    {% endif %}
                    
                    {% if can_manage or is_admin or user.user_type.code == 'SUPER_ADMIN' %}
                        <!-- No additional Manage Content button needed here as it's already in the admin actions section -->
                    {% elif has_paid %}
                        <a href="{% url 'website:course_content_list' course.course_id %}" class="w-full bg-white/10 hover:bg-white/20 text-white font-medium py-2 px-4 rounded-md block text-center">
                            {% trans "View All Content" %}
                        </a>
                    {% elif has_upcoming_reservation %}
                        <div class="w-full bg-amber-600/10 text-amber-500 font-medium py-2 px-4 rounded-md text-center">
                            {% trans "Content Available When Course Starts" %}
                        </div>
                    {% else %}
                        <div class="w-full bg-amber-600/10 text-amber-500 font-medium py-2 px-4 rounded-md text-center">
                            {% trans "Payment Required to Access Content" %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enroll Modal -->
{% if is_external_individual %}
<div id="enroll-modal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold text-white mb-4">{% trans "Choose Course Instance" %}</h3>
        <div class="mb-6">
            <select id="instance-select" class="w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>
            </select>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="closeEnrollModal()" class="px-4 py-2 text-white/70 hover:text-white">
                {% trans "Cancel" %}
            </button>
            <button onclick="enrollInInstance()" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                {% trans "Enroll" %}
            </button>
        </div>
    </div>
</div>

<script>
let selectedInstanceId = null;
let courseInstances = {
    {% for instance in course_instances %}
    "{{ instance.instance_id }}": {
        startDate: "{{ instance.start_date|date:"M d, Y" }}",
        startDateRaw: "{{ instance.start_date|date:"Y-m-d" }}",
        endDate: "{{ instance.end_date|date:"M d, Y" }}",
        sessionCount: {{ instance.session_count }},
        capacity: {{ instance.capacity }},
        availableSeats: {{ instance.available_seats }},
        waitingListCount: {{ instance.waiting_list_count }},
        cancellationDeadlinePassed: {% if instance.time_until_deadline == "Deadline has passed" %}true{% else %}false{% endif %}
    },
    {% endfor %}
};

// Track user enrollments
let userEnrollments = [
    {% for reservation in request.user.reservation_set.all %}
        {% if reservation.course_instance and reservation.status != 'CANCELLED' %}
            "{{ reservation.course_instance.instance_id }}",
        {% endif %}
    {% endfor %}
];

// Function to check if the start date has passed
function hasStartDatePassed(startDateRaw) {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day
    const startDate = new Date(startDateRaw);
    return today >= startDate;
}

// Function to show notification in the modal
function showModalNotification(message, type) {
    // Remove any existing notification
    const existingNotification = document.getElementById('modal-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create new notification
    const notification = document.createElement('div');
    notification.id = 'modal-notification';
    
    // Set styles based on type
    if (type === 'error') {
        notification.className = 'bg-red-500/10 border-red-500/30 text-red-400 p-3 rounded-md border mb-4';
    } else if (type === 'warning') {
        notification.className = 'bg-amber-500/10 border-amber-500/30 text-amber-400 p-3 rounded-md border mb-4';
    } else if (type === 'success') {
        notification.className = 'bg-green-500/10 border-green-500/30 text-green-400 p-3 rounded-md border mb-4';
    }
    
    notification.textContent = message;
    
    // Insert at the top of the modal content
    const modalContent = document.querySelector('#enroll-modal > div');
    modalContent.insertBefore(notification, modalContent.firstChild);
    
    // Set timeout based on notification type
    let timeout = 5000; // Default: 5 seconds
    
    if (type === 'success') {
        timeout = 5000; // 5 seconds for success
    } else if (type === 'warning') {
        timeout = 7000; // 7 seconds for warnings
    } else if (type === 'error') {
        timeout = 10000; // 10 seconds for errors
    }
    
    // Auto-dismiss all notifications after their timeout
    setTimeout(() => {
        notification.style.transition = 'opacity 0.5s ease';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 500);
    }, timeout);
}

function showEnrollModal() {
    // Reset the dropdown selection
    const instanceSelect = document.getElementById('instance-select');
    instanceSelect.innerHTML = '<option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>';
    
    // Populate the dropdown with available options
    Object.entries(courseInstances).forEach(([id, instance]) => {
        // Check if user is already enrolled (and not cancelled)
        const isEnrolled = userEnrollments.includes(id);
        const startDatePassed = hasStartDatePassed(instance.startDateRaw);
        
        // Skip instances where user is already enrolled or start date has passed
        if (isEnrolled || startDatePassed) {
            return; // Don't add this instance to the dropdown
        }
        
        const option = document.createElement('option');
        option.value = id;
        option.className = 'bg-gray-800';
        
        if (instance.availableSeats <= 0) {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, {% trans "Fully Booked" %})`;
            if (instance.waitingListCount > 0) {
                option.textContent += ` - {% trans "Waiting List" %}: ${instance.waitingListCount}`;
            }
        } else {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, ${instance.availableSeats} {% trans "seats left" %})`;
        }
        instanceSelect.appendChild(option);
    });

    // Reset selection
    selectedInstanceId = null;
    instanceSelect.value = '';

    // Add change event listener
    instanceSelect.addEventListener('change', (e) => {
        selectedInstanceId = e.target.value;
    });

    document.getElementById('enroll-modal').classList.remove('hidden');
    document.getElementById('enroll-modal').classList.add('flex');
}

function closeEnrollModal() {
    document.getElementById('enroll-modal').classList.add('hidden');
    document.getElementById('enroll-modal').classList.remove('flex');
    selectedInstanceId = null;
    
    // Remove any notifications
    const notification = document.getElementById('modal-notification');
    if (notification) {
        notification.remove();
    }
}

function enrollInInstance() {
    if (!selectedInstanceId) {
        showModalNotification('{% trans "Please select an instance" %}', 'warning');
        return;
    }
    
    // Check if already enrolled
    if (userEnrollments.includes(selectedInstanceId)) {
        showModalNotification('{% trans "You are already enrolled in this course instance" %}', 'warning');
        return;
    }
    
    const instance = courseInstances[selectedInstanceId];
    
    // Check if start date has passed
    if (instance && hasStartDatePassed(instance.startDateRaw)) {
        showModalNotification('{% trans "Enrollment for this course instance is closed because the course has already started." %}', 'error');
        return;
    }
    
    if (instance && instance.availableSeats <= 0) {
        // Ask if they want to join the waiting list instead
        if (confirm('{% trans "This course instance is fully booked. Would you like to join the waiting list instead?" %}')) {
            // Redirect to enrollment with waitlist parameter
            window.location.href = "{% url 'website:enroll_in_instance' 999999 %}?waitlist=yes".replace('999999', selectedInstanceId);
            return;
        } else {
            return; // User declined to join waitlist
        }
    }
    
    // All checks passed, proceed with enrollment
    window.location.href = "{% url 'website:enroll_in_instance' 999999 %}".replace('999999', selectedInstanceId);
}

// Cancellation Modal Functions
function openCancellationModal(reservationId) {
    document.getElementById('cancellation_reservation_id').value = reservationId;
    document.getElementById('cancellationConfirmModal').classList.remove('hidden');
}

function closeCancellationModal() {
    document.getElementById('cancellationConfirmModal').classList.add('hidden');
}

function openEmergencyCancellationModal(reservationId) {
    document.getElementById('emergency_reservation_id').value = reservationId;
    document.getElementById('emergencyCancellationModal').classList.remove('hidden');
}

function closeEmergencyCancellationModal() {
    document.getElementById('emergencyCancellationModal').classList.add('hidden');
}
</script>

<!-- Cancellation Confirmation Modal -->
<div id="cancellationConfirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-white">{% trans "Cancel Reservation" %}</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-300">{% trans "Are you sure you want to cancel this reservation? This action cannot be undone." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "Please note that cancellation policies may apply. You can cancel your reservation up to the cancellation deadline without penalty." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
                        </div>
                        <form id="cancellationForm" method="POST" action="{% url 'website:user_reservations' %}">
                            {% csrf_token %}
                            <input type="hidden" id="cancellation_reservation_id" name="reservation_id" value="">
                            <input type="hidden" name="cancel_reservation" value="true">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="document.getElementById('cancellationForm').submit();" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm Cancel" %}
                </button>
                <button type="button" onclick="closeCancellationModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Emergency Cancellation Modal -->
<div id="emergencyCancellationModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-orange-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-white">{% trans "Emergency Cancellation Request" %}</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-300">{% trans "Submit an emergency cancellation request for this reservation. This will be reviewed by our team." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "Please provide a detailed reason for your emergency cancellation request:" %}</p>
                        </div>
                        <form id="emergencyCancellationForm" method="POST" action="{% url 'website:emergency_cancellation_request' %}">
                            {% csrf_token %}
                            <input type="hidden" id="emergency_reservation_id" name="reservation_id" value="">
                            <div class="mt-4">
                                <textarea name="reason" rows="4" class="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="{% trans 'Please explain the emergency circumstances...' %}" required></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="document.getElementById('emergencyCancellationForm').submit();" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Submit Request" %}
                </button>
                <button type="button" onclick="closeEmergencyCancellationModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 